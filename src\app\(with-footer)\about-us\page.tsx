//src/app/(with-footer)/about-us/page.tsx
import React from "react";
import { Metadata } from "next";
import { generateDynamicMetadata } from "@/lib/metadata-utils";

export async function generateMetadata(): Promise<Metadata> {
  return await generateDynamicMetadata({
    title: "About Us - Academic Writing Excellence & Student Support",
    description: "Learn about our mission to empower academic success. Meet our expert team, discover our values, and see how we've helped 50,000+ students worldwide achieve their academic goals.",
    keywords: [
      "about us",
      "academic writing team",
      "student support",
      "educational excellence",
      "academic mission",
      "writing experts",
      "university support",
      "academic assistance team"
    ],
    path: "/about-us",
  });
}

"use client";

import Link from "next/link";
import {
  Card,
  CardContent,
  // CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  GraduationCap,
  Users,
  Award,
  BookOpen,
  Target,
  Heart,
  Globe,
  Star,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  Shield,
  Lightbulb,
} from "lucide-react";

const teamMembers = [
  {
    name: "Dr. Sarah Johnson",
    role: "Founder & CEO",
    image: "/api/placeholder/150/150",
    bio: "PhD in Educational Technology with 15+ years in academic writing support.",
    expertise: [
      "Academic Writing",
      "Research Methods",
      "Educational Leadership",
    ],
  },
  {
    name: "Prof. Michael Chen",
    role: "Head of Research",
    image: "/api/placeholder/150/150",
    bio: "Former university professor specializing in academic research and publication.",
    expertise: ["Research Design", "Data Analysis", "Publication Strategy"],
  },
  {
    name: "Dr. Emily Rodriguez",
    role: "Writing Director",
    image: "/api/placeholder/150/150",
    bio: "Expert in academic writing across multiple disciplines and citation styles.",
    expertise: ["Academic Writing", "Citation Styles", "Peer Review"],
  },
  {
    name: "James Wilson",
    role: "Technology Lead",
    image: "/api/placeholder/150/150",
    bio: "Full-stack developer passionate about educational technology solutions.",
    expertise: ["Web Development", "EdTech", "User Experience"],
  },
];

const achievements = [
  {
    icon: Users,
    label: "Students Helped",
    value: "50,000+",
    description: "Across 100+ universities worldwide",
  },
  {
    icon: BookOpen,
    label: "Papers Published",
    value: "25,000+",
    description: "Successfully guided to publication",
  },
  {
    icon: Award,
    label: "Success Rate",
    value: "98%",
    description: "Student satisfaction rating",
  },
  {
    icon: Globe,
    label: "Countries Served",
    value: "45+",
    description: "Global academic community",
  },
];

const values = [
  {
    icon: Target,
    title: "Excellence",
    description:
      "We strive for the highest quality in every piece of academic work we support.",
  },
  {
    icon: Heart,
    title: "Integrity",
    description:
      "We maintain the highest ethical standards and promote academic honesty.",
  },
  {
    icon: Shield,
    title: "Confidentiality",
    description:
      "Your privacy and academic work are protected with enterprise-grade security.",
  },
  {
    icon: Lightbulb,
    title: "Innovation",
    description:
      "We continuously evolve our methods to meet changing academic needs.",
  },
];

const milestones = [
  {
    year: "2018",
    event: "Company Founded",
    description:
      "Started with a vision to democratize academic writing support",
  },
  {
    year: "2019",
    event: "First 1,000 Students",
    description: "Reached our first major milestone in student support",
  },
  {
    year: "2021",
    event: "Global Expansion",
    description: "Extended services to universities across 5 continents",
  },
  {
    year: "2023",
    event: "AI Integration",
    description: "Launched AI-powered writing assistance tools",
  },
  {
    year: "2024",
    event: "50K+ Students",
    description: "Celebrating over 50,000 students helped worldwide",
  },
];

function AboutUsPageContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <section className="relative py-20 px-4 md:px-8 lg:px-16">
        <div className="max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <GraduationCap className="w-4 h-4" />
            About Our Academic Excellence
          </div>
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-6">
            Empowering Academic Success
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            We&apos;re dedicated to supporting students and researchers
            worldwide in achieving their academic goals through expert guidance,
            innovative tools, and personalized support.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 md:px-8 lg:px-16">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => {
              const IconComponent = achievement.icon;
              return (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <Card className="text-center hover:shadow-lg transition-all duration-300 cursor-pointer group">
                      <CardContent className="pt-6">
                        <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="w-6 h-6 text-primary" />
                        </div>
                        <div className="text-3xl font-bold text-primary mb-2">
                          {achievement.value}
                        </div>
                        <div className="font-semibold mb-1">
                          {achievement.label}
                        </div>
                      </CardContent>
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{achievement.description}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        </div>
      </section>

      {/* Main Content Tabs */}
      <section className="py-16 px-4 md:px-8 lg:px-16">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="story" className="w-full">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-4 h-auto p-1">
              <TabsTrigger
                value="story"
                className="flex items-center gap-2 py-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <BookOpen className="w-4 h-4" />
                Our Story
              </TabsTrigger>
              <TabsTrigger
                value="team"
                className="flex items-center gap-2 py-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <Users className="w-4 h-4" />
                Our Team
              </TabsTrigger>
              <TabsTrigger
                value="values"
                className="flex items-center gap-2 py-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <Heart className="w-4 h-4" />
                Our Values
              </TabsTrigger>
              <TabsTrigger
                value="timeline"
                className="flex items-center gap-2 py-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <Calendar className="w-4 h-4" />
                Timeline
              </TabsTrigger>
            </TabsList>

            <TabsContent value="story" className="mt-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5 text-primary" />
                      Our Mission
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      To democratize access to high-quality academic writing
                      support, empowering students and researchers worldwide to
                      communicate their ideas effectively and achieve their
                      academic aspirations.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-primary" />
                      Our Vision
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      To become the world&apos;s most trusted platform for
                      academic excellence, where every student has access to the
                      tools and guidance needed to succeed in their educational
                      journey.
                    </p>
                  </CardContent>
                </Card>

                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5 text-primary" />
                      Our Story
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      Founded in 2018 by a team of passionate educators and
                      technologists, our platform emerged from a simple
                      observation: many brilliant students struggle not with
                      their ideas, but with expressing them effectively in
                      academic writing.
                    </p>
                    <p className="text-muted-foreground leading-relaxed">
                      What started as a small tutoring service has grown into a
                      comprehensive academic support platform, serving over
                      50,000 students across 45+ countries. We&apos;ve
                      maintained our core commitment to personalized, ethical,
                      and high-quality academic assistance.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="team" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {teamMembers.map((member, index) => (
                  <Card
                    key={index}
                    className="text-center hover:shadow-lg transition-all duration-300"
                  >
                    <CardContent className="pt-6">
                      <Avatar className="w-24 h-24 mx-auto mb-4">
                        <AvatarImage src={member.image} alt={member.name} />
                        <AvatarFallback className="text-lg">
                          {member.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <h3 className="font-semibold text-lg mb-1">
                        {member.name}
                      </h3>
                      <p className="text-primary font-medium mb-3">
                        {member.role}
                      </p>
                      <p className="text-sm text-muted-foreground mb-4">
                        {member.bio}
                      </p>
                      <div className="flex flex-wrap gap-1 justify-center">
                        {member.expertise.map((skill, skillIndex) => (
                          <Badge
                            key={skillIndex}
                            variant="secondary"
                            className="text-xs"
                          >
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="values" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {values.map((value, index) => {
                  const IconComponent = value.icon;
                  return (
                    <Card
                      key={index}
                      className="hover:shadow-lg transition-all duration-300"
                    >
                      <CardHeader>
                        <CardTitle className="flex items-center gap-3">
                          <div className="inline-flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                            <IconComponent className="w-5 h-5 text-primary" />
                          </div>
                          {value.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground leading-relaxed">
                          {value.description}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="timeline" className="mt-8">
              <div className="space-y-6">
                {milestones.map((milestone, index) => (
                  <div key={index} className="flex gap-6 items-start">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-bold">
                        {milestone.year.slice(-2)}
                      </div>
                    </div>
                    <Card className="flex-1">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            {milestone.event}
                          </CardTitle>
                          <Badge variant="outline">{milestone.year}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">
                          {milestone.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="py-16 px-4 md:px-8 lg:px-16 bg-muted/30">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Start Your Academic Journey?
          </h2>
          <p className="text-muted-foreground mb-8 text-lg">
            Join thousands of students who have achieved their academic goals
            with our support.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/create-order">
                  <Button size="lg" className="gap-2">
                    <Mail className="w-4 h-4" />
                    Get Started Today
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Contact us to begin your academic success journey</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/contact-us">
                  <Button variant="outline" size="lg" className="gap-2">
                    <Phone className="w-4 h-4" />
                    Schedule a Consultation
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Book a free consultation with our experts</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <Separator className="my-8" />

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span>Global Support Available</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              <span>98% Success Rate</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              <span>100% Confidential</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function AboutUsPage() {
  return <AboutUsPageContent />;
}
