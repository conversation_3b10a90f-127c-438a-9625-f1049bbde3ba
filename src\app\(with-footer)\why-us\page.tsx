import { Suspense } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { Skeleton } from "@/components/ui/skeleton";
import { WhyUsPage } from "@/components/why-us/why-us-page";
import { generateDynamicMetadata } from "@/lib/metadata-utils";

export async function generateMetadata(): Promise<Metadata> {
  return await generateDynamicMetadata({
    title: "Why Choose Us - Expert Academic Writing Services",
    description: "Discover why thousands of students trust us for their academic writing needs. Expert writers, 24/7 support, plagiarism-free content, and guaranteed satisfaction.",
    keywords: [
      "why choose us",
      "academic writing services",
      "expert writers",
      "student support",
      "plagiarism free",
      "quality guarantee",
      "homework help",
      "essay writing",
      "research papers",
      "academic excellence"
    ],
    path: "/why-us",
    ogImage: "/images/why-us-og.jpg",
  });
}

function WhyUsSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Hero Section Skeleton */}
          <div className="text-center space-y-4">
            <Skeleton className="h-16 w-96 mx-auto" />
            <Skeleton className="h-6 w-[600px] mx-auto" />
            <Skeleton className="h-12 w-48 mx-auto" />
          </div>

          {/* Features Grid Skeleton */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
              </div>
            ))}
          </div>

          {/* Stats Section Skeleton */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="text-center space-y-2">
                <Skeleton className="h-12 w-16 mx-auto" />
                <Skeleton className="h-4 w-24 mx-auto" />
              </div>
            ))}
          </div>

          {/* Testimonials Skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-8 w-48 mx-auto" />
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function WhyUs() {
  return (
    <Suspense fallback={<WhyUsSkeleton />}>
      <WhyUsPage />
    </Suspense>
  );
}
