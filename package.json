{"name": "academic-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:socket": "node socket-server.mjs", "dev:full": "concurrently \"npm run dev\" \"npm run dev:socket\"", "postinstall": "npx prisma generate && npx prisma db seed", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "npx prisma db seed"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/supabase-js": "^2.50.0", "@tabler/icons-react": "^3.31.0", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.17.3", "lucide-react": "^0.503.0", "mongodb": "^6.16.0", "nanoid": "^5.1.5", "next": "^15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "prisma": "^6.7.0", "quill": "^2.0.3", "react": "^19.1.0", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-quill-new": "^3.4.6", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "concurrently": "^9.1.0", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "shadcn": "^2.5.0", "tailwindcss": "^4.1.4", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.8", "typescript": "^5"}, "optionalDependencies": {"@types/socket.io": "^3.0.1", "bufferutil": "^4.0.9", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "utf-8-validate": "^6.0.5"}}