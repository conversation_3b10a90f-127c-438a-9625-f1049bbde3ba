import { NotificationType } from "@prisma/client";
import prisma from "@/lib/prisma";
import { emailService, EmailNotificationData, InvoiceData } from "@/lib/email-service";

export interface NotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  taskId?: string;
  assignmentId?: string;
  emailData?: {
    userEmail: string;
    userName?: string;
    assignmentTitle?: string;
    status?: string;
    amount?: number;
    invoiceData?: InvoiceData;
    writerName?: string;
    clientName?: string;
    adminName?: string;
    dueDate?: string;
    customMessage?: string;
  };
}

// SSE connection management
interface SSEConnection {
  userId: string;
  response: Response;
  controller: ReadableStreamDefaultController;
}

// SSE notification payload interface
interface SSENotificationPayload {
  type: 'new-notification' | 'connection-established' | 'heartbeat';
  notification?: {
    id?: string;
    type: NotificationType;
    title: string;
    message: string;
    taskId?: string;
    assignmentId?: string;
    read: boolean;
    createdAt: string;
  };
  message?: string;
  timestamp?: string;
}

class NotificationService {
  private sseConnections: Map<string, SSEConnection[]> = new Map();

  // Add SSE connection for a user
  addSSEConnection(userId: string, response: Response, controller: ReadableStreamDefaultController) {
    if (!this.sseConnections.has(userId)) {
      this.sseConnections.set(userId, []);
    }
    this.sseConnections.get(userId)!.push({ userId, response, controller });
    console.log(`SSE connection added for user ${userId}`);
  }

  // Remove SSE connection for a user
  removeSSEConnection(userId: string, controller: ReadableStreamDefaultController) {
    const connections = this.sseConnections.get(userId);
    if (connections) {
      const index = connections.findIndex(conn => conn.controller === controller);
      if (index > -1) {
        connections.splice(index, 1);
        if (connections.length === 0) {
          this.sseConnections.delete(userId);
        }
        console.log(`SSE connection removed for user ${userId}`);
      }
    }
  }

  // Send real-time notification via SSE
  private sendSSENotification(userId: string, notification: SSENotificationPayload) {
    const connections = this.sseConnections.get(userId);
    if (connections && connections.length > 0) {
      const data = `data: ${JSON.stringify(notification)}\n\n`;
      connections.forEach(conn => {
        try {
          conn.controller.enqueue(new TextEncoder().encode(data));
        } catch (error) {
          console.error(`Error sending SSE to user ${userId}:`, error);
          // Remove failed connection
          this.removeSSEConnection(userId, conn.controller);
        }
      });
    }
  }

  async sendNotification(data: NotificationData): Promise<boolean> {
    try {
      // 1. Save notification to database
      const savedNotification = await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          taskId: data.taskId,
          assignmentId: data.assignmentId,
          read: false,
        },
      });

      // 2. Send email notification if email data is provided
      if (data.emailData) {
        // For writer payment notifications, extract payment method from customMessage
        let paymentAmount: string | undefined;
        let paymentMethod: string | undefined;

        if (data.type === NotificationType.PAYMENT_RECEIVED && data.emailData.amount && data.emailData.customMessage) {
          paymentAmount = data.emailData.amount.toFixed(2);
          // Extract payment method from customMessage (format: "Payment method: PayPal")
          const methodMatch = data.emailData.customMessage.match(/Payment method: (.+)/);
          paymentMethod = methodMatch ? methodMatch[1] : undefined;
        }

        const emailNotificationData: EmailNotificationData = {
          to: data.emailData.userEmail,
          type: data.type,
          data: {
            userName: data.emailData.userName,
            assignmentTitle: data.emailData.assignmentTitle,
            assignmentId: data.assignmentId,
            taskId: data.taskId,
            status: data.emailData.status,
            amount: data.emailData.amount,
            invoiceData: data.emailData.invoiceData,
            writerName: data.emailData.writerName,
            clientName: data.emailData.clientName,
            adminName: data.emailData.adminName,
            dueDate: data.emailData.dueDate,
            customMessage: data.emailData.customMessage,
            // Add payment fields for writer payment emails
            paymentAmount,
            paymentMethod,
          },
        };

        await emailService.sendNotificationEmail(emailNotificationData);
      }

      // 3. Send real-time notification via SSE
      this.sendSSENotification(data.userId, {
        type: 'new-notification',
        notification: {
          id: savedNotification.id,
          type: savedNotification.type,
          title: savedNotification.title,
          message: savedNotification.message,
          taskId: savedNotification.taskId || undefined,
          assignmentId: savedNotification.assignmentId || undefined,
          read: savedNotification.read,
          createdAt: savedNotification.createdAt.toISOString(),
        },
      });

      console.log(`Notification sent successfully to user ${data.userId}:`, data.title);
      return true;
    } catch (error) {
      console.error("Error sending notification:", error);
      return false;
    }
  }

  async sendWriterAssignedNotification(
    writerId: string,
    writerEmail: string,
    writerName: string,
    assignmentId: string,
    assignmentTitle: string,
    taskId: string,
    dueDate?: string
  ): Promise<boolean> {
    return this.sendNotification({
      userId: writerId,
      type: NotificationType.WRITER_ASSIGNED,
      title: "New Assignment Assigned",
      message: `You have been assigned to work on "${assignmentTitle}"`,
      taskId,
      assignmentId,
      emailData: {
        userEmail: writerEmail,
        userName: writerName,
        assignmentTitle,
        dueDate,
      },
    });
  }

  async sendStatusChangeNotification(
    userId: string,
    userEmail: string,
    userName: string,
    assignmentId: string,
    assignmentTitle: string,
    taskId: string,
    oldStatus: string,
    newStatus: string,
    customMessage?: string
  ): Promise<boolean> {
    const title = `Assignment Status Changed to ${newStatus}`;
    const message = `Assignment "${assignmentTitle}" status changed from ${oldStatus} to ${newStatus}`;

    return this.sendNotification({
      userId,
      type: NotificationType.STATUS_CHANGE,
      title,
      message,
      taskId,
      assignmentId,
      emailData: {
        userEmail,
        userName,
        assignmentTitle,
        status: newStatus,
        customMessage,
      },
    });
  }

  async sendJobPostedNotification(
    clientId: string,
    clientEmail: string,
    clientName: string,
    assignmentId: string,
    assignmentTitle: string,
    taskId: string,
    dueDate?: string
  ): Promise<boolean> {
    return this.sendNotification({
      userId: clientId,
      type: NotificationType.JOB_POSTED,
      title: "Job Posted Successfully",
      message: `Your assignment "${assignmentTitle}" has been posted and is now available for bidding`,
      taskId,
      assignmentId,
      emailData: {
        userEmail: clientEmail,
        userName: clientName,
        assignmentTitle,
        dueDate,
      },
    });
  }

  async sendJobCompletedNotification(
    userId: string,
    userEmail: string,
    userName: string,
    assignmentId: string,
    assignmentTitle: string,
    taskId: string,
    writerName?: string
  ): Promise<boolean> {
    return this.sendNotification({
      userId,
      type: NotificationType.JOB_COMPLETED,
      title: "Assignment Completed",
      message: `Assignment "${assignmentTitle}" has been completed`,
      taskId,
      assignmentId,
      emailData: {
        userEmail,
        userName,
        assignmentTitle,
        writerName,
      },
    });
  }

  async sendPaymentReceivedNotification(
    userId: string,
    userEmail: string,
    userName: string,
    assignmentId: string,
    assignmentTitle: string,
    taskId: string,
    amount: number,
    invoiceData?: InvoiceData
  ): Promise<boolean> {
    return this.sendNotification({
      userId,
      type: NotificationType.PAYMENT_RECEIVED,
      title: "Payment Received",
      message: `Payment of $${amount.toFixed(2)} received for "${assignmentTitle}"`,
      taskId,
      assignmentId,
      emailData: {
        userEmail,
        userName,
        assignmentTitle,
        amount,
        invoiceData,
      },
    });
  }

  // Helper method to send notifications to multiple users (e.g., all admins)
  async sendNotificationToMultipleUsers(
    userIds: string[],
    type: NotificationType,
    title: string,
    message: string,
    taskId?: string,
    assignmentId?: string
  ): Promise<boolean> {
    try {
      const notifications = userIds.map(userId => ({
        userId,
        type,
        title,
        message,
        taskId,
        assignmentId,
        read: false,
      }));

      await prisma.notification.createMany({
        data: notifications,
      });

      // Send real-time notifications via SSE
      userIds.forEach(userId => {
        this.sendSSENotification(userId, {
          type: 'new-notification',
          notification: {
            type,
            title,
            message,
            taskId: taskId || undefined,
            assignmentId: assignmentId || undefined,
            read: false,
            createdAt: new Date().toISOString(),
          },
        });
      });

      console.log(`Bulk notifications sent to ${userIds.length} users:`, title);
      return true;
    } catch (error) {
      console.error("Error sending bulk notifications:", error);
      return false;
    }
  }

  // Helper method to get all admin user IDs
  async getAllAdminIds(): Promise<string[]> {
    try {
      const admins = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      });
      return admins.map(admin => admin.id);
    } catch (error) {
      console.error("Error fetching admin IDs:", error);
      return [];
    }
  }

  // Helper method to get all admin users with email info
  async getAllAdmins(): Promise<Array<{ id: string; email: string; name: string | null }>> {
    try {
      const admins = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true, email: true, name: true },
      });
      return admins;
    } catch (error) {
      console.error("Error fetching admins:", error);
      return [];
    }
  }
}

export const notificationService = new NotificationService();
