import { Metadata } from "next";
import { generateDynamicMetadata } from "./metadata-utils";

// Metadata configurations for all public routes
export const routeMetadata = {
  "/": {
    title: "Professional Academic Writing Services - Expert Help for Students",
    description: "Get expert help with essays, research papers, dissertations, and more. Professional academic writers, 24/7 support, plagiarism-free content, and on-time delivery guaranteed.",
    keywords: [
      "academic writing services",
      "essay help",
      "research paper writing",
      "dissertation assistance",
      "homework help",
      "professional writers",
      "student support",
      "academic assistance",
      "college essay help",
      "university writing services"
    ]
  },
  "/about-us": {
    title: "About Us - Academic Writing Excellence & Student Support",
    description: "Learn about our mission to empower academic success. Meet our expert team, discover our values, and see how we've helped 50,000+ students worldwide achieve their academic goals.",
    keywords: [
      "about us",
      "academic writing team",
      "student support",
      "educational excellence",
      "academic mission",
      "writing experts",
      "university support",
      "academic assistance team"
    ]
  },
  "/contact-us": {
    title: "Contact Us - Get Academic Writing Help & Support",
    description: "Contact our academic writing experts for immediate assistance. 24/7 support available via chat, email, and phone. Get your questions answered and start your order today.",
    keywords: [
      "contact us",
      "academic writing support",
      "customer service",
      "24/7 help",
      "writing assistance",
      "student support",
      "academic help contact"
    ]
  },
  "/why-us": {
    title: "Why Choose Us - Top Academic Writing Service Benefits",
    description: "Discover why students choose our academic writing service. Expert writers, plagiarism-free content, on-time delivery, 24/7 support, and money-back guarantee.",
    keywords: [
      "why choose us",
      "academic writing benefits",
      "expert writers",
      "plagiarism free",
      "on time delivery",
      "money back guarantee",
      "student benefits"
    ]
  },
  "/how-it-works": {
    title: "How It Works - Simple Steps to Academic Writing Success",
    description: "Learn how our academic writing service works. Simple 3-step process: place order, work with expert writer, receive your paper. Fast, secure, and reliable.",
    keywords: [
      "how it works",
      "academic writing process",
      "order process",
      "writing steps",
      "academic assistance process",
      "student guide"
    ]
  },
  "/careers": {
    title: "Careers - Join Our Academic Writing Team",
    description: "Join our team of expert academic writers. We're looking for qualified professionals to help students achieve academic success. Apply today for flexible, rewarding work.",
    keywords: [
      "careers",
      "academic writing jobs",
      "freelance writing",
      "writer positions",
      "academic careers",
      "writing opportunities"
    ]
  },
  "/create-order": {
    title: "Create Order - Start Your Academic Writing Project",
    description: "Create your academic writing order in minutes. Choose your paper type, deadline, and requirements. Get matched with expert writers and receive high-quality work.",
    keywords: [
      "create order",
      "place order",
      "academic writing order",
      "essay order",
      "research paper order",
      "dissertation order"
    ]
  },
  "/services": {
    title: "Academic Writing Services - Essays, Research Papers & More",
    description: "Comprehensive academic writing services including essays, research papers, dissertations, case studies, and more. Expert writers for all academic levels and subjects.",
    keywords: [
      "academic writing services",
      "essay writing",
      "research papers",
      "dissertations",
      "case studies",
      "academic papers",
      "writing help"
    ]
  },
  "/services/custom-essay-writing": {
    title: "Custom Essay Writing Service - Professional Essay Help",
    description: "Professional custom essay writing service. Expert writers create original, high-quality essays tailored to your requirements. All academic levels and subjects covered.",
    keywords: [
      "custom essay writing",
      "essay help",
      "professional essays",
      "academic essays",
      "college essays",
      "university essays"
    ]
  },
  "/services/dissertation": {
    title: "Dissertation Writing Service - Expert PhD & Masters Help",
    description: "Professional dissertation writing service for PhD and Masters students. Expert writers with advanced degrees provide comprehensive dissertation support.",
    keywords: [
      "dissertation writing",
      "PhD dissertation",
      "Masters dissertation",
      "dissertation help",
      "academic dissertation",
      "thesis writing"
    ]
  },
  "/services/research-paper": {
    title: "Research Paper Writing Service - Academic Research Help",
    description: "Professional research paper writing service. Expert researchers and writers create original, well-researched papers with proper citations and formatting.",
    keywords: [
      "research paper writing",
      "academic research",
      "research help",
      "scientific papers",
      "research assistance",
      "academic research papers"
    ]
  },
  "/services/literature-review": {
    title: "Literature Review Writing Service - Academic Review Help",
    description: "Professional literature review writing service. Expert writers conduct comprehensive literature analysis and create well-structured academic reviews.",
    keywords: [
      "literature review",
      "academic review",
      "literature analysis",
      "research review",
      "systematic review",
      "academic literature"
    ]
  },
  "/services/term-paper": {
    title: "Term Paper Writing Service - Academic Term Paper Help",
    description: "Professional term paper writing service. Expert writers create high-quality term papers for all academic subjects and levels with proper research and formatting.",
    keywords: [
      "term paper writing",
      "academic term papers",
      "semester papers",
      "college term papers",
      "university term papers",
      "term paper help"
    ]
  },
  "/testimonials": {
    title: "Student Testimonials - Academic Writing Success Stories",
    description: "Read real testimonials from students who achieved academic success with our writing service. See why thousands of students trust us with their academic work.",
    keywords: [
      "testimonials",
      "student reviews",
      "success stories",
      "academic writing reviews",
      "customer feedback",
      "student experiences"
    ]
  },
  "/terms": {
    title: "Terms of Service - Academic Writing Service Terms",
    description: "Read our terms of service for academic writing assistance. Understand our policies, guarantees, and service conditions for a transparent experience.",
    keywords: [
      "terms of service",
      "service terms",
      "academic writing terms",
      "policies",
      "service conditions"
    ]
  },
  "/privacy-policy": {
    title: "Privacy Policy - Your Data Protection & Security",
    description: "Our privacy policy explains how we protect your personal information and academic work. We maintain strict confidentiality and data security standards.",
    keywords: [
      "privacy policy",
      "data protection",
      "confidentiality",
      "security",
      "personal information",
      "academic privacy"
    ]
  },
  "/refund-policy": {
    title: "Refund Policy - Money-Back Guarantee Terms",
    description: "Our refund policy ensures your satisfaction with our academic writing service. Learn about our money-back guarantee and refund conditions.",
    keywords: [
      "refund policy",
      "money back guarantee",
      "refund terms",
      "satisfaction guarantee",
      "academic writing refunds"
    ]
  },
  "/cookies-policy": {
    title: "Cookies Policy - Website Cookie Usage Information",
    description: "Learn about how we use cookies on our academic writing service website to improve your experience and provide personalized service.",
    keywords: [
      "cookies policy",
      "website cookies",
      "cookie usage",
      "web tracking",
      "user experience"
    ]
  },
  "/code-of-conduct": {
    title: "Code of Conduct - Academic Integrity & Ethics",
    description: "Our code of conduct outlines our commitment to academic integrity, ethical practices, and responsible academic assistance.",
    keywords: [
      "code of conduct",
      "academic integrity",
      "ethics",
      "academic ethics",
      "responsible assistance",
      "academic standards"
    ]
  }
};

// Helper function to generate metadata for any route
export async function getRouteMetadata(path: string): Promise<Metadata> {
  const config = routeMetadata[path as keyof typeof routeMetadata];
  
  if (!config) {
    // Fallback metadata for routes not in the config
    return await generateDynamicMetadata({
      title: "Academic Writing Services",
      description: "Professional academic writing assistance for students at all levels.",
      keywords: ["academic writing", "student help", "essay assistance"],
      path,
    });
  }

  return await generateDynamicMetadata({
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    path,
  });
}
