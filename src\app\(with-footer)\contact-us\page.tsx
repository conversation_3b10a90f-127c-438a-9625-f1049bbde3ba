import React, { Suspense, lazy } from "react";
import type { Metadata } from "next";

// Lazy load components for better performance
const ContactHero = lazy(() => import("@/components/contact/contact-hero").then(mod => ({ default: mod.ContactHero })));
const ContactForm = lazy(() => import("@/components/contact/contact-form").then(mod => ({ default: mod.ContactForm })));
const ContactInfo = lazy(() => import("@/components/contact/contact-info").then(mod => ({ default: mod.ContactInfo })));
const ContactFAQ = lazy(() => import("@/components/contact/contact-faq").then(mod => ({ default: mod.ContactFAQ })));

// Loading placeholder component
const SectionLoader = () => (
  <div className="w-full flex justify-center items-center py-12">
    <div className="h-12 w-12 rounded-full border-4 border-b-transparent border-primary animate-spin"></div>
  </div>
);

// SEO Metadata
export const metadata: Metadata = {
  title: "Contact Us - Get Expert Academic Writing Help | Homework Asylum",
  description: "Contact our expert academic writing team for personalized assistance. Available 24/7 via live chat, email, or phone. Fast response times and professional support guaranteed.",
  keywords: [
    "contact academic writing service",
    "homework help support",
    "essay writing assistance",
    "academic support team",
    "student help contact",
    "writing service customer service",
    "24/7 academic support",
    "homework asylum contact"
  ],
  authors: [{ name: "Homework Asylum" }],
  creator: "Homework Asylum",
  publisher: "Homework Asylum",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    title: "Contact Us - Expert Academic Writing Support",
    description: "Get in touch with our academic writing experts. 24/7 support available via multiple channels. Fast, reliable, and professional assistance.",
    url: "https://homeworkassylum.com/contact-us",
    siteName: "Homework Asylum",
    images: [
      {
        url: "/images/contact-og.jpg",
        width: 1200,
        height: 630,
        alt: "Contact Homework Asylum - Academic Writing Support",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Contact Us - Expert Academic Writing Support",
    description: "Get in touch with our academic writing experts. 24/7 support available.",
    images: ["/images/contact-twitter.jpg"],
    creator: "@homeworkassylum",
  },
  alternates: {
    canonical: "https://homeworkassylum.com/contact-us",
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": "Contact Us - Homework Asylum",
  "description": "Contact our expert academic writing team for personalized assistance with your homework and essays.",
  "url": "https://homeworkassylum.com/contact-us",
  "mainEntity": {
    "@type": "Organization",
    "name": "Homework Asylum",
    "url": "https://homeworkassylum.com",
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "******-123-4567",
        "contactType": "customer service",
        "availableLanguage": ["English"],
        "hoursAvailable": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday", 
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday"
          ],
          "opens": "00:00",
          "closes": "23:59"
        }
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer service",
        "availableLanguage": ["English"]
      }
    ],
    "sameAs": [
      "https://facebook.com/homeworkassylum",
      "https://twitter.com/homeworkassylum",
      "https://linkedin.com/company/homeworkassylum"
    ]
  }
};

export default function ContactUsPage() {
  return (
    <>
      {/* JSON-LD structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      <div className="min-h-screen">
        {/* Hero Section */}
        <Suspense fallback={<SectionLoader />}>
          <ContactHero />
        </Suspense>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-16 space-y-16">
          {/* Contact Form and Info Section */}
          <section id="contact-form" className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Left Column - Contact Form and FAQ */}
            <div className="lg:col-span-2 space-y-12">
              <Suspense fallback={<SectionLoader />}>
                <ContactForm />
              </Suspense>

              {/* FAQ Section - Moved here */}
              <div id="faq">
                <Suspense fallback={<SectionLoader />}>
                  <ContactFAQ />
                </Suspense>
              </div>
            </div>

            {/* Right Column - Contact Info */}
            <div className="lg:col-span-1">
              <Suspense fallback={<SectionLoader />}>
                <ContactInfo />
              </Suspense>
            </div>
          </section>

          {/* Additional Support Section */}
          <section className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              Need Immediate Assistance?
            </h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our support team is available 24/7 to help you with any questions or concerns. 
              Don$apos;t hesitate to reach out - we$apos;re here to ensure your academic success!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
              >
                Email Support
              </a>
              <a
                href="tel:+15551234567"
                className="inline-flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-semibold"
              >
                Call Now
              </a>
            </div>
          </section>
        </main>
      </div>
    </>
  );
}
