import { ImageResponse } from 'next/og'
 
export const runtime = 'edge'
 
export const alt = 'Academic Writing Services - Professional Help for Students'
export const size = {
  width: 1200,
  height: 630,
}
 
export const contentType = 'image/png'
 
export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #1e293b 0%, #3730a3 50%, #1e40af 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          fontFamily: 'system-ui',
          color: 'white',
          position: 'relative',
        }}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%)',
          }}
        />
        
        {/* Main Content */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            zIndex: 1,
            padding: '40px',
          }}
        >
          {/* Title */}
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              marginBottom: '20px',
              background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',
              backgroundClip: 'text',
              color: 'transparent',
              lineHeight: 1.1,
            }}
          >
            Academic Writing
          </h1>
          
          {/* Subtitle */}
          <p
            style={{
              fontSize: '32px',
              marginBottom: '30px',
              opacity: 0.9,
              maxWidth: '800px',
              lineHeight: 1.3,
            }}
          >
            Professional Help for Students at All Levels
          </p>
          
          {/* Features */}
          <div
            style={{
              display: 'flex',
              gap: '40px',
              fontSize: '20px',
              opacity: 0.8,
            }}
          >
            <span>✓ Expert Writers</span>
            <span>✓ 24/7 Support</span>
            <span>✓ Plagiarism-Free</span>
            <span>✓ On-Time Delivery</span>
          </div>
        </div>
        
        {/* Bottom Badge */}
        <div
          style={{
            position: 'absolute',
            bottom: '40px',
            right: '40px',
            background: 'rgba(255,255,255,0.1)',
            padding: '12px 24px',
            borderRadius: '25px',
            fontSize: '18px',
            fontWeight: '600',
          }}
        >
          Trusted by 150K+ Students
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
